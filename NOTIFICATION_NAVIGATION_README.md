# OneSignal Notification Navigation Implementation

This document explains how the OneSignal notification navigation has been implemented in your Flutter app.

## Overview

The implementation allows users to navigate to specific pages when they click on OneSignal push notifications. The navigation is handled automatically based on the notification type and target ID contained in the notification payload.

## Files Modified/Created

### 1. `lib/services/notification_navigation_service.dart` (NEW)
This is the main service that handles notification navigation logic.

**Key Features:**
- Parses notification payload to extract custom data
- Routes to appropriate pages based on notification type
- Handles unknown notification types gracefully
- Provides detailed logging for debugging

### 2. `lib/main.dart` (MODIFIED)
Updated to use the notification navigation service:
- Added import for `NotificationNavigationService`
- Updated OneSignal click listener to call `NotificationNavigationService.handleNotificationClick()`
- Updated foreground notification listener to call `NotificationNavigationService.handleForegroundNotification()`

### 3. `lib/ui/home2.dart` (MODIFIED)
Removed duplicate OneSignal notification listeners since they're now handled in `main.dart`.

### 4. `lib/test/notification_test.dart` (NEW)
A test page to simulate different notification types for testing purposes.

## Notification Types Supported

Based on your notification logs and existing code, the following notification types are supported:

| Type | Description | Navigation Target |
|------|-------------|-------------------|
| 2 | Order Confirmed | ConfirmedOrdersView with order ID |
| 3 | Order Status Update | ConfirmedOrdersView with order ID |
| 4 | Order Preparing/Picking Up/In Transit | ConfirmedOrdersView with order ID |
| 13 | Catering Request Accepted | CateringRequestsPage (Accepted tab) |
| 14 | Catering Request Completed | CateringRequestsPage (Past Orders tab) |
| Other | Unknown notification types | Shows dialog with notification info |

## How It Works

### 1. Notification Payload Structure
Your notifications contain custom data in this format:
```json
{
  "custom": "{\"i\":\"notification-id\",\"a\":{\"target_id\":440,\"type\":4,\"user_id\":1,\"user_role\":\"CUSTOMER\"}}"
}
```

The service extracts the data from the `"a"` field which contains:
- `target_id`: The ID of the order/catering request
- `type`: The notification type (determines navigation)
- `user_id`: The user ID
- `user_role`: The user role (e.g., "CUSTOMER")

### 2. Navigation Flow
1. User clicks on notification
2. OneSignal triggers the click listener in `main.dart`
3. `NotificationNavigationService.handleNotificationClick()` is called
4. Service parses the notification payload
5. Service extracts notification type and target ID
6. Service navigates to appropriate page based on type

### 3. Error Handling
- If parsing fails, error is logged and no navigation occurs
- If notification type is unknown, a dialog is shown
- If target ID is missing/invalid for order notifications, error is logged

## Testing

### Using the Test Page
1. Add the test page to your app navigation
2. Navigate to the test page
3. Click different buttons to simulate notification types
4. Observe the navigation behavior

### Example Test Integration
```dart
// Add this to your app's navigation or debug menu
Navigator.push(
  context,
  MaterialPageRoute(builder: (context) => const NotificationTestPage()),
);
```

### Real Notification Testing
1. Send test notifications from OneSignal dashboard
2. Include the proper custom data format
3. Click notifications to test navigation
4. Check logs for debugging information

## Debugging

The service provides extensive logging. Look for these log messages:

```
Processing notification click: [notification data]
Parsing notification data...
AdditionalData: [data]
RawPayload: [data]
Notification type: X, Target ID: Y, User ID: Z, User Role: CUSTOMER
Navigating to [destination] for [type]: [id]
```

## Customization

### Adding New Notification Types
1. Add new case to the switch statement in `_navigateBasedOnType()`
2. Create navigation method for the new page
3. Update the documentation

### Modifying Navigation Logic
Edit the `_navigateBasedOnType()` method in `NotificationNavigationService` to change how notifications are handled.

### Custom Error Handling
Modify `_showUnknownNotificationDialog()` to customize how unknown notification types are handled.

## Example Notification Payloads

### Order Confirmed (Type 2)
```json
{
  "title": "Order Confirmed",
  "alert": "Your order #DA-1753846835586-139 has been confirmed by the chef.",
  "custom": "{\"i\":\"notification-id\",\"a\":{\"target_id\":440,\"type\":2,\"user_id\":1,\"user_role\":\"CUSTOMER\"}}"
}
```

### Order Preparing (Type 4)
```json
{
  "title": "Order is Preparing",
  "alert": "Your order #DA-1753846835586-139 is preparing by chef.",
  "custom": "{\"i\":\"notification-id\",\"a\":{\"target_id\":440,\"type\":4,\"user_id\":1,\"user_role\":\"CUSTOMER\"}}"
}
```

### Catering Accepted (Type 13)
```json
{
  "title": "Catering Request Accepted",
  "alert": "Your catering request has been accepted.",
  "custom": "{\"i\":\"notification-id\",\"a\":{\"target_id\":123,\"type\":13,\"user_id\":1,\"user_role\":\"CUSTOMER\"}}"
}
```

## Troubleshooting

### Notification Not Navigating
1. Check if OneSignal is properly initialized
2. Verify notification payload contains custom data
3. Check logs for parsing errors
4. Ensure NavigationService.navigatorKey is properly set

### Wrong Page Navigation
1. Verify notification type in payload
2. Check switch statement logic
3. Ensure target ID is correct

### App Crashes on Navigation
1. Check if target pages exist and are properly imported
2. Verify navigation context is available
3. Check for null values in navigation parameters

## Future Enhancements

1. **Deep Linking**: Integrate with Flutter deep linking for better navigation
2. **Notification History**: Store clicked notifications for user reference
3. **Custom Actions**: Add action buttons to notifications
4. **Rich Notifications**: Support for images and expanded content
5. **Analytics**: Track notification click rates and user engagement
