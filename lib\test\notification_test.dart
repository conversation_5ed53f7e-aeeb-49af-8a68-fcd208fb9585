import 'dart:convert';
import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:db_eats/services/notification_navigation_service.dart';
import 'package:onesignal_flutter/onesignal_flutter.dart';

/// Test page to simulate notification clicks for testing purposes
class NotificationTestPage extends StatefulWidget {
  const NotificationTestPage({super.key});

  @override
  State<NotificationTestPage> createState() => _NotificationTestPageState();
}

class _NotificationTestPageState extends State<NotificationTestPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Notification Test'),
        backgroundColor: Colors.black,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const Text(
              'Test Notification Navigation',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 20),
            const Text(
              'Click the buttons below to simulate different notification types:',
              style: TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 20),
            
            // Order Confirmed Test
            ElevatedButton(
              onPressed: () => _testNotification(
                type: 2,
                targetId: 440,
                title: 'Order Confirmed',
                message: 'Your order #DA-1753846835586-139 has been confirmed by the chef.',
              ),
              style: ElevatedButton.styleFrom(backgroundColor: Colors.green),
              child: const Text('Test Order Confirmed (Type 2)', style: TextStyle(color: Colors.white)),
            ),
            
            const SizedBox(height: 10),
            
            // Order Preparing Test
            ElevatedButton(
              onPressed: () => _testNotification(
                type: 4,
                targetId: 440,
                title: 'Order is Preparing',
                message: 'Your order #DA-1753846835586-139 is preparing by chef.',
              ),
              style: ElevatedButton.styleFrom(backgroundColor: Colors.orange),
              child: const Text('Test Order Preparing (Type 4)', style: TextStyle(color: Colors.white)),
            ),
            
            const SizedBox(height: 10),
            
            // Order Picking Up Test
            ElevatedButton(
              onPressed: () => _testNotification(
                type: 4,
                targetId: 440,
                title: 'Order is Picking Up',
                message: 'Your order #DA-1753846835586-139 is picking up driver.',
              ),
              style: ElevatedButton.styleFrom(backgroundColor: Colors.blue),
              child: const Text('Test Order Picking Up (Type 4)', style: TextStyle(color: Colors.white)),
            ),
            
            const SizedBox(height: 10),
            
            // Catering Accepted Test
            ElevatedButton(
              onPressed: () => _testNotification(
                type: 13,
                targetId: 123,
                title: 'Catering Request Accepted',
                message: 'Your catering request has been accepted.',
              ),
              style: ElevatedButton.styleFrom(backgroundColor: Colors.purple),
              child: const Text('Test Catering Accepted (Type 13)', style: TextStyle(color: Colors.white)),
            ),
            
            const SizedBox(height: 10),
            
            // Catering Completed Test
            ElevatedButton(
              onPressed: () => _testNotification(
                type: 14,
                targetId: 123,
                title: 'Catering Order Completed',
                message: 'Your catering order has been completed.',
              ),
              style: ElevatedButton.styleFrom(backgroundColor: Colors.teal),
              child: const Text('Test Catering Completed (Type 14)', style: TextStyle(color: Colors.white)),
            ),
            
            const SizedBox(height: 10),
            
            // Unknown Type Test
            ElevatedButton(
              onPressed: () => _testNotification(
                type: 99,
                targetId: 123,
                title: 'Unknown Notification',
                message: 'This is an unknown notification type.',
              ),
              style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
              child: const Text('Test Unknown Type (Type 99)', style: TextStyle(color: Colors.white)),
            ),
            
            const SizedBox(height: 30),
            
            const Text(
              'Note: These tests simulate notification clicks and will navigate to the appropriate pages based on the notification type.',
              style: TextStyle(fontSize: 14, fontStyle: FontStyle.italic),
            ),
          ],
        ),
      ),
    );
  }

  void _testNotification({
    required int type,
    required int targetId,
    required String title,
    required String message,
  }) {
    log('Testing notification: Type $type, Target ID $targetId');
    
    // Create a mock notification similar to the real OneSignal format
    final customData = {
      'target_id': targetId,
      'type': type,
      'user_id': 1,
      'user_role': 'CUSTOMER',
    };
    
    // Create a mock OSNotification
    // Note: Since OSNotification is from OneSignal SDK, we'll simulate the call directly
    try {
      // Simulate the notification click handling
      _simulateNotificationClick(customData);
    } catch (e) {
      log('Error testing notification: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error testing notification: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _simulateNotificationClick(Map<String, dynamic> customData) {
    // Extract notification type and target ID
    final int? notificationType = customData['type'];
    final int? targetId = customData['target_id'];
    final int? userId = customData['user_id'];
    final String? userRole = customData['user_role'];

    log('Simulated notification - Type: $notificationType, Target ID: $targetId, User ID: $userId, User Role: $userRole');

    // Call the navigation service directly with the parsed data
    if (notificationType != null) {
      _navigateBasedOnType(context, notificationType, targetId);
    }
  }

  // Copy of the navigation logic from NotificationNavigationService for testing
  void _navigateBasedOnType(BuildContext context, int notificationType, int? targetId) {
    switch (notificationType) {
      case 2: // Order confirmed
      case 3: // Order status update
      case 4: // Order preparing/picking up/delivered
        log('Navigating to order details for order: $targetId');
        // Navigate to order details - you can import and use the actual navigation here
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Would navigate to Order Details for Order ID: $targetId'),
            backgroundColor: Colors.green,
          ),
        );
        break;
        
      case 13: // Catering request accepted
        log('Navigating to catering requests - accepted tab');
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Would navigate to Catering Requests - Accepted Tab'),
            backgroundColor: Colors.purple,
          ),
        );
        break;
        
      case 14: // Catering request completed
        log('Navigating to catering requests - past orders tab');
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Would navigate to Catering Requests - Past Orders Tab'),
            backgroundColor: Colors.teal,
          ),
        );
        break;
        
      default:
        log('Unknown notification type: $notificationType');
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Unknown notification type: $notificationType'),
            backgroundColor: Colors.red,
          ),
        );
        break;
    }
  }
}
